import { bootstrapMock } from "@skywind-group/sw-integration-core";
import { MockModule } from "./mock/mock.module";
import config from "./config";

async function bootstrap() {
    console.log("🚀 Starting PCES Mock Server...");
    
    const app = await bootstrapMock({
        serviceName: "pces-mock-server",
        versionFile: "./lib/version",
        module: MockModule,
        internalPort: config.internalServer.port,
        port: config.server.mockPort,
        actions: ["auth", "debit", "credit", "debit-credit", "rollback", "promo"],
        doNotStartApplication: true
    });

    await app.listen(config.server.mockPort, config.server.host);
    
    console.log(`✅ PCES Mock Server running on http://${config.server.host}:${config.server.mockPort}`);
    console.log(`📊 Internal server running on port ${config.internalServer.port}`);
    console.log(`🎯 Available PCES endpoints:`);
    console.log(`   POST /auth - Authentication and balance retrieval`);
    console.log(`   POST /debit - Bet placement (debit player balance)`);
    console.log(`   POST /credit - Win settlement (credit player balance)`);
    console.log(`   POST /debit-credit - Combined bet and settlement`);
    console.log(`   POST /rollback - Transaction reversal`);
    console.log(`   POST /promo - Promotional wins (freespins, jackpots, etc.)`);
    console.log(`📖 Documentation: See docs/PCES_MOCK_SERVER.md`);
}

bootstrap().catch((error) => {
    console.error("❌ Failed to start PCES Mock Server:", error);
    process.exit(1);
});
