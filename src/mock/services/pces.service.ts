import { Injectable } from "@nestjs/common";
import { MockDataService } from "../data/mock-data.service";
import { HashValidatorService } from "../utils/hash-validator";
import { ResponseBuilderService } from "../utils/response-builder";
import {
    OperatorAuthRequest,
    OperatorAuthResponse,
    OperatorDebitRequest,
    OperatorDebitResponse,
    OperatorCreditRequest,
    OperatorCreditResponse,
    OperatorDebitCreditRequest,
    OperatorDebitCreditResponse,
    OperatorRollbackRequest,
    OperatorRollbackResponse,
    OperatorPromoRequest,
    OperatorPromoResponse,
    operatorErrorCodes
} from "@entities/operator.entities";

@Injectable()
export class PCESService {
    constructor(
        private readonly mockDataService: MockDataService,
        private readonly hashValidator: HashValidatorService,
        private readonly responseBuilder: ResponseBuilderService
    ) {}

    async authenticate(request: OperatorAuthRequest, genericId: string, hash: string): Promise<OperatorAuthResponse> {
        // Validate hash
        this.hashValidator.validateHash(request, genericId, hash);

        // Get player data
        const player = this.mockDataService.getPlayer(request.customer);
        if (!player) {
            throw this.responseBuilder.createError(operatorErrorCodes.CUSTOMER_NOT_FOUND, "Customer not found");
        }

        // Validate token
        if (!this.mockDataService.isValidToken(request.token, request.customer)) {
            throw this.responseBuilder.createError(operatorErrorCodes.TOKEN_INVALID, "Invalid token");
        }

        return this.responseBuilder.buildAuthResponse(player);
    }

    async debit(request: OperatorDebitRequest, genericId: string, hash: string): Promise<OperatorDebitResponse> {
        // Validate hash
        this.hashValidator.validateHash(request, genericId, hash);

        // Get player data
        const player = this.mockDataService.getPlayer(request.customer);
        if (!player) {
            throw this.responseBuilder.createError(operatorErrorCodes.CUSTOMER_NOT_FOUND, "Customer not found");
        }

        // Validate token
        if (!this.mockDataService.isValidToken(request.token, request.customer)) {
            throw this.responseBuilder.createError(operatorErrorCodes.TOKEN_INVALID, "Invalid token");
        }

        // Check if bet already exists
        if (this.mockDataService.betExists(request.betId)) {
            throw this.responseBuilder.createError(operatorErrorCodes.BET_ALREADY_SETTLED, "Bet already exists");
        }

        // Check sufficient funds
        const totalBalance = player.balance + player.bonusBalance;
        if (totalBalance < request.amount) {
            throw this.responseBuilder.createError(operatorErrorCodes.INSUFFICIENT_FUNDS, "Insufficient funds");
        }

        // Process debit
        const transaction = this.mockDataService.createDebitTransaction(request);
        this.mockDataService.updatePlayerBalance(request.customer, -request.amount);
        
        const updatedPlayer = this.mockDataService.getPlayer(request.customer)!;
        return this.responseBuilder.buildDebitResponse(updatedPlayer, transaction.id);
    }

    async credit(request: OperatorCreditRequest, genericId: string, hash: string): Promise<OperatorCreditResponse> {
        // Validate hash
        this.hashValidator.validateHash(request, genericId, hash);

        // Get player data
        const player = this.mockDataService.getPlayer(request.customer);
        if (!player) {
            throw this.responseBuilder.createError(operatorErrorCodes.CUSTOMER_NOT_FOUND, "Customer not found");
        }

        // Validate token
        if (!this.mockDataService.isValidToken(request.token, request.customer)) {
            throw this.responseBuilder.createError(operatorErrorCodes.TOKEN_INVALID, "Invalid token");
        }

        // Check if bet exists
        if (!this.mockDataService.betExists(request.betId)) {
            throw this.responseBuilder.createError(operatorErrorCodes.BET_RECORD_NOT_FOUND, "Bet record not found");
        }

        // Check if bet already won
        if (this.mockDataService.betAlreadyWon(request.betId)) {
            throw this.responseBuilder.createError(operatorErrorCodes.BET_ALREADY_WON, "Bet already won");
        }

        // Process credit
        const transaction = this.mockDataService.createCreditTransaction(request);
        this.mockDataService.updatePlayerBalance(request.customer, request.amount);
        this.mockDataService.markBetAsWon(request.betId);
        
        const updatedPlayer = this.mockDataService.getPlayer(request.customer)!;
        return this.responseBuilder.buildCreditResponse(updatedPlayer, transaction.id);
    }

    async debitCredit(request: OperatorDebitCreditRequest, genericId: string, hash: string): Promise<OperatorDebitCreditResponse> {
        // Validate hash
        this.hashValidator.validateHash(request, genericId, hash);

        // Get player data
        const player = this.mockDataService.getPlayer(request.customer);
        if (!player) {
            throw this.responseBuilder.createError(operatorErrorCodes.CUSTOMER_NOT_FOUND, "Customer not found");
        }

        // Validate token
        if (!this.mockDataService.isValidToken(request.token, request.customer)) {
            throw this.responseBuilder.createError(operatorErrorCodes.TOKEN_INVALID, "Invalid token");
        }

        // Check if bet already exists
        if (this.mockDataService.betExists(request.betId)) {
            throw this.responseBuilder.createError(operatorErrorCodes.BET_ALREADY_SETTLED, "Bet already exists");
        }

        // Check sufficient funds for debit
        const totalBalance = player.balance + player.bonusBalance;
        if (totalBalance < request.amount) {
            throw this.responseBuilder.createError(operatorErrorCodes.INSUFFICIENT_FUNDS, "Insufficient funds");
        }

        // Process debit-credit transaction
        const debitTransaction = this.mockDataService.createDebitTransaction({
            ...request,
            trxId: request.trxId
        });

        const creditTransaction = this.mockDataService.createCreditTransaction({
            ...request,
            amount: request.creditAmount,
            trxId: request.creditTrxId
        });

        // Update balance: debit first, then credit
        this.mockDataService.updatePlayerBalance(request.customer, -request.amount + request.creditAmount);
        this.mockDataService.markBetAsWon(request.betId);

        const updatedPlayer = this.mockDataService.getPlayer(request.customer)!;
        return this.responseBuilder.buildDebitCreditResponse(updatedPlayer, debitTransaction.id, creditTransaction.id);
    }

    async rollback(request: OperatorRollbackRequest, genericId: string, hash: string): Promise<OperatorRollbackResponse> {
        // Validate hash
        this.hashValidator.validateHash(request, genericId, hash);

        // Get player data
        const player = this.mockDataService.getPlayer(request.customer);
        if (!player) {
            throw this.responseBuilder.createError(operatorErrorCodes.CUSTOMER_NOT_FOUND, "Customer not found");
        }

        // Validate token
        if (!this.mockDataService.isValidToken(request.token, request.customer)) {
            throw this.responseBuilder.createError(operatorErrorCodes.TOKEN_INVALID, "Invalid token");
        }

        // Find transaction to rollback
        const transaction = this.mockDataService.getTransactionByTrxId(request.trxId);
        if (!transaction) {
            throw this.responseBuilder.createError(operatorErrorCodes.TRANSACTION_NOT_FOUND, "Transaction not found");
        }

        // Process rollback
        const rollbackTransaction = this.mockDataService.createRollbackTransaction(request, transaction);

        // Reverse the original transaction
        const reverseAmount = transaction.type === 'debit' ? transaction.amount : -transaction.amount;
        this.mockDataService.updatePlayerBalance(request.customer, reverseAmount);
        this.mockDataService.markTransactionAsRolledBack(transaction.id);

        const updatedPlayer = this.mockDataService.getPlayer(request.customer)!;
        return this.responseBuilder.buildRollbackResponse(updatedPlayer, rollbackTransaction.id);
    }

    async promo(request: OperatorPromoRequest, genericId: string, hash: string): Promise<OperatorPromoResponse> {
        // Validate hash
        this.hashValidator.validateHash(request, genericId, hash);

        // Get player data
        const player = this.mockDataService.getPlayer(request.customer);
        if (!player) {
            throw this.responseBuilder.createError(operatorErrorCodes.CUSTOMER_NOT_FOUND, "Customer not found");
        }

        // Validate token
        if (!this.mockDataService.isValidToken(request.token, request.customer)) {
            throw this.responseBuilder.createError(operatorErrorCodes.TOKEN_INVALID, "Invalid token");
        }

        // Validate promo type
        if (!this.mockDataService.isValidPromoType(request.promo.promoType)) {
            throw this.responseBuilder.createError(operatorErrorCodes.PROMOTION_TYPE_NOT_SUPPORTED, "Promotion type not supported");
        }

        // Process promotional win
        const transaction = this.mockDataService.createPromoTransaction(request);
        this.mockDataService.updatePlayerBalance(request.customer, request.amount);

        const updatedPlayer = this.mockDataService.getPlayer(request.customer)!;
        return this.responseBuilder.buildPromoResponse(updatedPlayer, transaction.id);
    }
}
