import { ArgumentsHost, Catch, ExceptionFilter, HttpStatus } from "@nestjs/common";
import { Response } from "express";
import { operatorErrorCodes, operatorStatusMessages } from "@entities/operator.entities";

@Catch()
export class PCESErrorFilter implements ExceptionFilter {
    catch(exception: any, host: ArgumentsHost): any {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<Response>();
        const request = ctx.getRequest();

        console.error(`[PCES ERROR] ${request.method} ${request.url}:`, exception.message);
        console.error(`[PCES ERROR] Stack:`, exception.stack);

        // Handle PCES-specific errors
        if (exception.code !== undefined && exception.status !== undefined) {
            return this.handlePCESError(response, exception);
        }

        // Handle validation errors
        if (exception.message && exception.message.includes('validation')) {
            return this.handleValidationError(response, exception);
        }

        // Handle authentication errors
        if (exception.message && (
            exception.message.includes('token') ||
            exception.message.includes('auth') ||
            exception.message.includes('hash')
        )) {
            return this.handleAuthError(response, exception);
        }

        // Handle insufficient funds
        if (exception.message && exception.message.includes('insufficient')) {
            return this.handleInsufficientFundsError(response, exception);
        }

        // Handle not found errors
        if (exception.message && (
            exception.message.includes('not found') ||
            exception.message.includes('does not exist')
        )) {
            return this.handleNotFoundError(response, exception);
        }

        // Default error handling
        return this.handleGenericError(response, exception);
    }

    private handlePCESError(response: Response, exception: any): void {
        const errorResponse = {
            balance: 0,
            bonusBalance: 0,
            code: exception.code,
            currency: "EUR", // Default currency for error responses
            status: exception.status
        };

        // Map PCES error codes to HTTP status codes
        const httpStatus = this.mapPCESCodeToHttpStatus(exception.code);
        
        response.status(httpStatus).json(errorResponse);
    }

    private handleValidationError(response: Response, exception: any): void {
        const errorResponse = {
            balance: 0,
            bonusBalance: 0,
            code: operatorErrorCodes.REQUIRED_FIELD_MISSING,
            currency: "EUR",
            status: operatorStatusMessages[operatorErrorCodes.REQUIRED_FIELD_MISSING]
        };

        response.status(HttpStatus.BAD_REQUEST).json(errorResponse);
    }

    private handleAuthError(response: Response, exception: any): void {
        let errorCode = operatorErrorCodes.AUTHENTICATION_FAILED;
        
        if (exception.message.includes('token')) {
            if (exception.message.includes('invalid')) {
                errorCode = operatorErrorCodes.TOKEN_INVALID;
            } else if (exception.message.includes('timeout') || exception.message.includes('expired')) {
                errorCode = operatorErrorCodes.TOKEN_TIMEOUT;
            } else if (exception.message.includes('not found')) {
                errorCode = operatorErrorCodes.TOKEN_NOT_FOUND;
            }
        } else if (exception.message.includes('hash')) {
            errorCode = operatorErrorCodes.UNAUTHORIZED_REQUEST;
        }

        const errorResponse = {
            balance: 0,
            bonusBalance: 0,
            code: errorCode,
            currency: "EUR",
            status: operatorStatusMessages[errorCode]
        };

        response.status(HttpStatus.UNAUTHORIZED).json(errorResponse);
    }

    private handleInsufficientFundsError(response: Response, exception: any): void {
        const errorResponse = {
            balance: 0,
            bonusBalance: 0,
            code: operatorErrorCodes.INSUFFICIENT_FUNDS,
            currency: "EUR",
            status: operatorStatusMessages[operatorErrorCodes.INSUFFICIENT_FUNDS]
        };

        response.status(HttpStatus.BAD_REQUEST).json(errorResponse);
    }

    private handleNotFoundError(response: Response, exception: any): void {
        let errorCode = operatorErrorCodes.UNKNOWN_ERROR;

        if (exception.message.includes('customer') || exception.message.includes('player')) {
            errorCode = operatorErrorCodes.CUSTOMER_NOT_FOUND;
        } else if (exception.message.includes('transaction')) {
            errorCode = operatorErrorCodes.TRANSACTION_NOT_FOUND;
        } else if (exception.message.includes('bet')) {
            errorCode = operatorErrorCodes.BET_RECORD_NOT_FOUND;
        } else if (exception.message.includes('game')) {
            errorCode = operatorErrorCodes.GAME_NOT_FOUND;
        }

        const errorResponse = {
            balance: 0,
            bonusBalance: 0,
            code: errorCode,
            currency: "EUR",
            status: operatorStatusMessages[errorCode]
        };

        response.status(HttpStatus.NOT_FOUND).json(errorResponse);
    }

    private handleGenericError(response: Response, exception: any): void {
        const errorResponse = {
            balance: 0,
            bonusBalance: 0,
            code: operatorErrorCodes.UNKNOWN_ERROR,
            currency: "EUR",
            status: operatorStatusMessages[operatorErrorCodes.UNKNOWN_ERROR]
        };

        response.status(HttpStatus.INTERNAL_SERVER_ERROR).json(errorResponse);
    }

    private mapPCESCodeToHttpStatus(pcesCode: number): HttpStatus {
        const codeMap: Record<number, HttpStatus> = {
            [operatorErrorCodes.SUCCESS]: HttpStatus.OK,
            [operatorErrorCodes.UNKNOWN_ERROR]: HttpStatus.INTERNAL_SERVER_ERROR,
            [operatorErrorCodes.INTERNAL_CACHE_ERROR]: HttpStatus.INTERNAL_SERVER_ERROR,
            [operatorErrorCodes.DATA_OUT_OF_RANGE]: HttpStatus.BAD_REQUEST,
            [operatorErrorCodes.UNAUTHORIZED_REQUEST]: HttpStatus.UNAUTHORIZED,
            [operatorErrorCodes.NOT_INTEGRATED]: HttpStatus.SERVICE_UNAVAILABLE,
            [operatorErrorCodes.TOKEN_CUSTOMER_MISMATCH]: HttpStatus.UNAUTHORIZED,
            [operatorErrorCodes.UNSUPPORTED_API_VERSION]: HttpStatus.BAD_REQUEST,
            [operatorErrorCodes.PROMOTION_TYPE_NOT_SUPPORTED]: HttpStatus.BAD_REQUEST,
            [operatorErrorCodes.BET_RECORD_NOT_FOUND]: HttpStatus.NOT_FOUND,
            [operatorErrorCodes.TRANSACTION_NOT_FOUND]: HttpStatus.NOT_FOUND,
            [operatorErrorCodes.BET_ALREADY_WON]: HttpStatus.CONFLICT,
            [operatorErrorCodes.BET_ALREADY_SETTLED]: HttpStatus.CONFLICT,
            [operatorErrorCodes.AUTHENTICATION_FAILED]: HttpStatus.UNAUTHORIZED,
            [operatorErrorCodes.GAME_NOT_FOUND]: HttpStatus.NOT_FOUND,
            [operatorErrorCodes.INVALID_GAME]: HttpStatus.BAD_REQUEST,
            [operatorErrorCodes.BET_LIMIT_REACHED]: HttpStatus.BAD_REQUEST,
            [operatorErrorCodes.LOSS_LIMIT_REACHED]: HttpStatus.BAD_REQUEST,
            [operatorErrorCodes.SESSION_LIMIT_REACHED]: HttpStatus.BAD_REQUEST,
            [operatorErrorCodes.PROFIT_LIMIT_REACHED]: HttpStatus.BAD_REQUEST,
            [operatorErrorCodes.INVALID_CASINO_VENDOR]: HttpStatus.BAD_REQUEST,
            [operatorErrorCodes.ALL_BET_ARE_OFF]: HttpStatus.SERVICE_UNAVAILABLE,
            [operatorErrorCodes.CUSTOMER_NOT_FOUND]: HttpStatus.NOT_FOUND,
            [operatorErrorCodes.INVALID_CURRENCY]: HttpStatus.BAD_REQUEST,
            [operatorErrorCodes.INSUFFICIENT_FUNDS]: HttpStatus.BAD_REQUEST,
            [operatorErrorCodes.PLAYER_SUSPENDED]: HttpStatus.FORBIDDEN,
            [operatorErrorCodes.REQUIRED_FIELD_MISSING]: HttpStatus.BAD_REQUEST,
            [operatorErrorCodes.TOKEN_NOT_FOUND]: HttpStatus.UNAUTHORIZED,
            [operatorErrorCodes.TOKEN_TIMEOUT]: HttpStatus.UNAUTHORIZED,
            [operatorErrorCodes.TOKEN_INVALID]: HttpStatus.UNAUTHORIZED,
            [operatorErrorCodes.NEGATIVE_DEPOSIT]: HttpStatus.BAD_REQUEST
        };

        return codeMap[pcesCode] || HttpStatus.INTERNAL_SERVER_ERROR;
    }
}
