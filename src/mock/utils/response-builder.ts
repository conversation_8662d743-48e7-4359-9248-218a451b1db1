import { Injectable } from "@nestjs/common";
import {
    OperatorAuthResponse,
    OperatorDebitResponse,
    OperatorCreditResponse,
    OperatorDebitCreditResponse,
    OperatorRollbackResponse,
    OperatorPromoResponse,
    operatorErrorCodes,
    operatorStatusMessages
} from "@entities/operator.entities";
import { MockPlayer } from "../data/mock-data.service";

@Injectable()
export class ResponseBuilderService {

    /**
     * Builds authentication response
     */
    buildAuthResponse(player: MockPlayer): OperatorAuthResponse {
        return {
            balance: this.roundAmount(player.balance),
            bonusBalance: this.roundAmount(player.bonusBalance),
            code: operatorErrorCodes.SUCCESS,
            currency: player.currency,
            status: operatorStatusMessages[operatorErrorCodes.SUCCESS],
            traderId: player.traderId
        };
    }

    /**
     * Builds debit response
     */
    buildDebitResponse(player: MockPlayer, trxId: number): OperatorDebitResponse {
        return {
            balance: this.roundAmount(player.balance),
            bonusBalance: this.roundAmount(player.bonusBalance),
            code: operatorErrorCodes.SUCCESS,
            currency: player.currency,
            status: operatorStatusMessages[operatorErrorCodes.SUCCESS],
            trxId: trxId
        };
    }

    /**
     * Builds credit response
     */
    buildCreditResponse(player: MockPlayer, trxId?: number): OperatorCreditResponse {
        return {
            balance: this.roundAmount(player.balance),
            bonusBalance: this.roundAmount(player.bonusBalance),
            code: operatorErrorCodes.SUCCESS,
            currency: player.currency,
            status: operatorStatusMessages[operatorErrorCodes.SUCCESS],
            trxId: trxId
        };
    }

    /**
     * Builds debit-credit response
     */
    buildDebitCreditResponse(player: MockPlayer, debitTrxId: number, creditTrxId: number): OperatorDebitCreditResponse {
        return {
            balance: this.roundAmount(player.balance),
            bonusBalance: this.roundAmount(player.bonusBalance),
            code: operatorErrorCodes.SUCCESS,
            currency: player.currency,
            status: operatorStatusMessages[operatorErrorCodes.SUCCESS],
            trxId: debitTrxId,
            creditTrxId: creditTrxId
        };
    }

    /**
     * Builds rollback response
     */
    buildRollbackResponse(player: MockPlayer, trxId: number): OperatorRollbackResponse {
        return {
            balance: this.roundAmount(player.balance),
            bonusBalance: this.roundAmount(player.bonusBalance),
            code: operatorErrorCodes.SUCCESS,
            currency: player.currency,
            status: operatorStatusMessages[operatorErrorCodes.SUCCESS],
            trxId: trxId
        };
    }

    /**
     * Builds promo response
     */
    buildPromoResponse(player: MockPlayer, trxId: number): OperatorPromoResponse {
        return {
            balance: this.roundAmount(player.balance),
            bonusBalance: this.roundAmount(player.bonusBalance),
            code: operatorErrorCodes.SUCCESS,
            currency: player.currency,
            status: operatorStatusMessages[operatorErrorCodes.SUCCESS],
            trxId: trxId
        };
    }

    /**
     * Creates an error response
     */
    createError(code: number, message?: string): Error {
        const errorMessage = message || operatorStatusMessages[code] || "Unknown error";
        const error = new Error(errorMessage);
        (error as any).code = code;
        (error as any).status = this.getStatusFromCode(code);
        return error;
    }

    /**
     * Builds error response object
     */
    buildErrorResponse(code: number, message?: string, currency: string = "EUR"): any {
        return {
            balance: 0,
            bonusBalance: 0,
            code: code,
            currency: currency,
            status: message || operatorStatusMessages[code] || "Unknown error"
        };
    }

    /**
     * Rounds amount to 2 decimal places to avoid floating point precision issues
     */
    private roundAmount(amount: number): number {
        return Math.round(amount * 100) / 100;
    }

    /**
     * Gets status string from error code
     */
    private getStatusFromCode(code: number): string {
        const statusMap: Record<number, string> = {
            [operatorErrorCodes.SUCCESS]: "SUCCESS",
            [operatorErrorCodes.UNKNOWN_ERROR]: "UNKNOWN_ERROR",
            [operatorErrorCodes.INTERNAL_CACHE_ERROR]: "INTERNAL_CACHE_ERROR",
            [operatorErrorCodes.DATA_OUT_OF_RANGE]: "DATA_OUT_OF_RANGE",
            [operatorErrorCodes.UNAUTHORIZED_REQUEST]: "UNAUTHORIZED_REQUEST",
            [operatorErrorCodes.NOT_INTEGRATED]: "NOT_INTEGRATED",
            [operatorErrorCodes.TOKEN_CUSTOMER_MISMATCH]: "TOKEN_CUSTOMER_MISMATCH",
            [operatorErrorCodes.UNSUPPORTED_API_VERSION]: "UNSUPPORTED_API_VERSION",
            [operatorErrorCodes.PROMOTION_TYPE_NOT_SUPPORTED]: "PROMOTION_TYPE_NOT_SUPPORTED",
            [operatorErrorCodes.BET_RECORD_NOT_FOUND]: "BET_RECORD_NOT_FOUND",
            [operatorErrorCodes.TRANSACTION_NOT_FOUND]: "TRANSACTION_NOT_FOUND",
            [operatorErrorCodes.BET_ALREADY_WON]: "BET_ALREADY_WON",
            [operatorErrorCodes.BET_ALREADY_SETTLED]: "BET_ALREADY_SETTLED",
            [operatorErrorCodes.AUTHENTICATION_FAILED]: "AUTHENTICATION_FAILED",
            [operatorErrorCodes.GAME_NOT_FOUND]: "GAME_NOT_FOUND",
            [operatorErrorCodes.INVALID_GAME]: "INVALID_GAME",
            [operatorErrorCodes.BET_LIMIT_REACHED]: "BET_LIMIT_REACHED",
            [operatorErrorCodes.LOSS_LIMIT_REACHED]: "LOSS_LIMIT_REACHED",
            [operatorErrorCodes.SESSION_LIMIT_REACHED]: "SESSION_LIMIT_REACHED",
            [operatorErrorCodes.PROFIT_LIMIT_REACHED]: "PROFIT_LIMIT_REACHED",
            [operatorErrorCodes.INVALID_CASINO_VENDOR]: "INVALID_CASINO_VENDOR",
            [operatorErrorCodes.ALL_BET_ARE_OFF]: "ALL_BET_ARE_OFF",
            [operatorErrorCodes.CUSTOMER_NOT_FOUND]: "CUSTOMER_NOT_FOUND",
            [operatorErrorCodes.INVALID_CURRENCY]: "INVALID_CURRENCY",
            [operatorErrorCodes.INSUFFICIENT_FUNDS]: "INSUFFICIENT_FUNDS",
            [operatorErrorCodes.PLAYER_SUSPENDED]: "PLAYER_SUSPENDED",
            [operatorErrorCodes.REQUIRED_FIELD_MISSING]: "REQUIRED_FIELD_MISSING",
            [operatorErrorCodes.TOKEN_NOT_FOUND]: "TOKEN_NOT_FOUND",
            [operatorErrorCodes.TOKEN_TIMEOUT]: "TOKEN_TIMEOUT",
            [operatorErrorCodes.TOKEN_INVALID]: "TOKEN_INVALID",
            [operatorErrorCodes.NEGATIVE_DEPOSIT]: "NEGATIVE_DEPOSIT"
        };

        return statusMap[code] || "UNKNOWN_ERROR";
    }

    /**
     * Validates required fields in request
     */
    validateRequiredFields(request: any, requiredFields: string[]): void {
        for (const field of requiredFields) {
            if (request[field] === undefined || request[field] === null || request[field] === '') {
                throw this.createError(operatorErrorCodes.REQUIRED_FIELD_MISSING, `Required field missing: ${field}`);
            }
        }
    }

    /**
     * Validates amount is positive
     */
    validatePositiveAmount(amount: number): void {
        if (amount <= 0) {
            throw this.createError(operatorErrorCodes.DATA_OUT_OF_RANGE, "Amount must be positive");
        }
    }

    /**
     * Validates currency format
     */
    validateCurrency(currency: string): void {
        const validCurrencies = ['EUR', 'USD', 'GBP', 'TRY', 'SEK', 'NOK', 'DKK', 'PLN', 'CZK', 'HUF', 'RON', 'BGN'];
        if (!validCurrencies.includes(currency)) {
            throw this.createError(operatorErrorCodes.INVALID_CURRENCY, `Invalid currency: ${currency}`);
        }
    }

    /**
     * Simulates network latency for more realistic testing
     */
    async simulateLatency(minMs: number = 50, maxMs: number = 200): Promise<void> {
        if (process.env.NODE_ENV === 'test') {
            return; // Skip latency in tests
        }

        const latency = Math.floor(Math.random() * (maxMs - minMs + 1)) + minMs;
        await new Promise(resolve => setTimeout(resolve, latency));
    }
}
