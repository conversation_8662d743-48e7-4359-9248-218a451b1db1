import { Injectable } from "@nestjs/common";
import * as crypto from "crypto";
import { operatorErrorCodes } from "@entities/operator.entities";

@Injectable()
export class HashValidatorService {
    // Mock secret key for hash validation - in real implementation this would come from config
    private readonly secretKey = "mock_secret_key_for_testing";

    /**
     * Validates HMAC-SHA256 hash for PCES API requests
     * @param requestBody - The request body object
     * @param genericId - Generic-Id header value
     * @param providedHash - Hash header value
     */
    validateHash(requestBody: any, genericId: string, providedHash: string): void {
        if (!genericId) {
            throw this.createHashError("Generic-Id header is required");
        }

        if (!providedHash) {
            throw this.createHashError("Hash header is required");
        }

        // Skip hash validation in development mode for easier testing
        if (process.env.NODE_ENV === 'development' && process.env.SKIP_HASH_VALIDATION === 'true') {
            console.log("[HASH VALIDATOR] Skipping hash validation in development mode");
            return;
        }

        try {
            const calculatedHash = this.calculateHash(requestBody, genericId);
            
            if (calculatedHash !== providedHash) {
                console.error(`[HASH VALIDATOR] Hash mismatch - Expected: ${calculatedHash}, Received: ${providedHash}`);
                throw this.createHashError("Invalid hash");
            }

            console.log("[HASH VALIDATOR] Hash validation successful");
        } catch (error) {
            if (error.message.includes("Invalid hash") || error.message.includes("required")) {
                throw error;
            }
            console.error("[HASH VALIDATOR] Hash validation error:", error);
            throw this.createHashError("Hash validation failed");
        }
    }

    /**
     * Calculates HMAC-SHA256 hash for the given request
     * @param requestBody - The request body object
     * @param genericId - Generic-Id header value
     * @returns Calculated hash string
     */
    calculateHash(requestBody: any, genericId: string): string {
        // Create the string to hash according to PCES API specification
        const stringToHash = this.createHashString(requestBody, genericId);
        
        // Calculate HMAC-SHA256
        const hmac = crypto.createHmac('sha256', this.secretKey);
        hmac.update(stringToHash);
        const hash = hmac.digest('hex').toUpperCase();
        
        console.log(`[HASH VALIDATOR] String to hash: ${stringToHash}`);
        console.log(`[HASH VALIDATOR] Calculated hash: ${hash}`);
        
        return hash;
    }

    /**
     * Creates the string to hash according to PCES API specification
     * The string is created by concatenating all request parameters in alphabetical order
     * @param requestBody - The request body object
     * @param genericId - Generic-Id header value
     * @returns String to be hashed
     */
    private createHashString(requestBody: any, genericId: string): string {
        // Add genericId to the request data for hashing
        const dataToHash = {
            ...requestBody,
            genericId: genericId
        };

        // Sort keys alphabetically and concatenate values
        const sortedKeys = Object.keys(dataToHash).sort();
        const values: string[] = [];

        for (const key of sortedKeys) {
            const value = dataToHash[key];
            if (value !== null && value !== undefined) {
                if (typeof value === 'object') {
                    // For nested objects (like promo data), stringify and sort
                    values.push(this.stringifyObject(value));
                } else {
                    values.push(String(value));
                }
            }
        }

        return values.join('');
    }

    /**
     * Stringifies an object with sorted keys for consistent hashing
     * @param obj - Object to stringify
     * @returns Stringified object
     */
    private stringifyObject(obj: any): string {
        if (obj === null || obj === undefined) {
            return '';
        }

        if (typeof obj !== 'object') {
            return String(obj);
        }

        if (Array.isArray(obj)) {
            return obj.map(item => this.stringifyObject(item)).join('');
        }

        const sortedKeys = Object.keys(obj).sort();
        const parts: string[] = [];

        for (const key of sortedKeys) {
            const value = obj[key];
            if (value !== null && value !== undefined) {
                parts.push(this.stringifyObject(value));
            }
        }

        return parts.join('');
    }

    /**
     * Creates a hash validation error
     * @param message - Error message
     * @returns Error object
     */
    private createHashError(message: string): Error {
        const error = new Error(message);
        (error as any).code = operatorErrorCodes.UNAUTHORIZED_REQUEST;
        (error as any).status = "UNAUTHORIZED_REQUEST";
        return error;
    }

    /**
     * Generates a valid hash for testing purposes
     * @param requestBody - The request body object
     * @param genericId - Generic-Id header value
     * @returns Valid hash for the request
     */
    generateValidHash(requestBody: any, genericId: string): string {
        return this.calculateHash(requestBody, genericId);
    }

    /**
     * Validates if the provided secret key matches the expected one
     * This is useful for testing different scenarios
     * @param secretKey - Secret key to validate
     * @returns True if valid, false otherwise
     */
    isValidSecretKey(secretKey: string): boolean {
        return secretKey === this.secretKey;
    }

    /**
     * Updates the secret key (for testing purposes)
     * @param newSecretKey - New secret key
     */
    updateSecretKey(newSecretKey: string): void {
        (this as any).secretKey = newSecretKey;
    }

    /**
     * Gets the current secret key (for testing purposes)
     * @returns Current secret key
     */
    getSecretKey(): string {
        return this.secretKey;
    }
}
