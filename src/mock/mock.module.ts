import { MiddlewareConsumer, Module, NestModule } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { PCESController } from "./controllers/pces.controller";
import { PCESService } from "./services/pces.service";
import { MockDataService } from "./data/mock-data.service";
import { HashValidatorService } from "./utils/hash-validator";
import { ResponseBuilderService } from "./utils/response-builder";
import { AuthResponder } from "./responders/auth.responder";
import { DebitResponder } from "./responders/debit.responder";
import { CreditResponder } from "./responders/credit.responder";
import { DebitCreditResponder } from "./responders/debit-credit.responder";
import { RollbackResponder } from "./responders/rollback.responder";
import { PromoResponder } from "./responders/promo.responder";
import { PCESErrorFilter } from "./filters/pces-error.filter";
import { HashValidationMiddleware } from "./middleware/hash-validation.middleware";

@Module({
    controllers: [PCESController],
    providers: [
        PCESService,
        MockDataService,
        HashValidatorService,
        ResponseBuilderService,
        AuthResponder,
        DebitResponder,
        CreditResponder,
        DebitCreditResponder,
        RollbackResponder,
        PromoResponder,
        PCESErrorFilter,
        HashValidationMiddleware
    ],
    imports: [mock.MockModule]
})
export class MockModule implements NestModule {
    configure(consumer: MiddlewareConsumer): any {
        // Apply hash validation middleware to all PCES endpoints
        consumer
            .apply(HashValidationMiddleware)
            .forRoutes("/auth", "/debit", "/credit", "/debit-credit", "/rollback", "/promo");

        // Apply specific responders to each endpoint
        consumer
            .apply(AuthResponder).forRoutes("/auth")
            .apply(DebitResponder).forRoutes("/debit")
            .apply(CreditResponder).forRoutes("/credit")
            .apply(DebitCreditResponder).forRoutes("/debit-credit")
            .apply(RollbackResponder).forRoutes("/rollback")
            .apply(PromoResponder).forRoutes("/promo");
    }
}
