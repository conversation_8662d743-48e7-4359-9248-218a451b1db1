import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { PCESService } from "../services/pces.service";
import { OperatorDebitRequest, OperatorDebitResponse } from "@entities/operator.entities";
import * as http from "http";

@Injectable()
export class DebitResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(
        settingsService: mock.SettingsService,
        private readonly pcesService: PCESService
    ) {
        super(settingsService);
    }

    public async getResponse(req: http.IncomingMessage, body: OperatorDebitRequest): Promise<OperatorDebitResponse> {
        console.log(`[DEBIT RESPONDER] Processing debit request for customer: ${body.customer}, amount: ${body.amount}`);

        // Extract headers
        const genericId = req.headers['generic-id'] as string;
        const hash = req.headers['hash'] as string;

        // Validate required fields
        this.validateRequiredFields(body);

        // Process debit
        const response = await this.pcesService.debit(body, genericId, hash);
        
        console.log(`[DEBIT RESPONDER] Debit successful for customer: ${body.customer}, New balance: ${response.balance}, TrxId: ${response.trxId}`);
        
        return response;
    }

    private validateRequiredFields(body: OperatorDebitRequest): void {
        const requiredFields = ['customer', 'token', 'gameId', 'amount', 'currency', 'betId', 'trxId'];
        
        for (const field of requiredFields) {
            if (!body[field as keyof OperatorDebitRequest]) {
                throw new Error(`Required field missing: ${field}`);
            }
        }

        // Validate amount is positive
        if (body.amount <= 0) {
            throw new Error("Amount must be positive");
        }
    }
}
