import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { PCESService } from "../services/pces.service";
import { OperatorRollbackRequest, OperatorRollbackResponse } from "@entities/operator.entities";
import * as http from "http";

@Injectable()
export class RollbackR<PERSON>ponder extends mock.NotSaveAnyDataMiddleware {
    constructor(
        settingsService: mock.SettingsService,
        private readonly pcesService: PCESService
    ) {
        super(settingsService);
    }

    public async getResponse(req: http.IncomingMessage, body: OperatorRollbackRequest): Promise<OperatorRollbackResponse> {
        console.log(`[ROLLBACK RESPONDER] Processing rollback request for customer: ${body.customer}, trxId: ${body.trxId}`);

        // Extract headers
        const genericId = req.headers['generic-id'] as string;
        const hash = req.headers['hash'] as string;

        // Validate required fields
        this.validateRequiredFields(body);

        // Process rollback
        const response = await this.pcesService.rollback(body, genericId, hash);
        
        console.log(`[ROLLBACK RESPONDER] Rollback successful for customer: ${body.customer}, New balance: ${response.balance}, TrxId: ${response.trxId}`);
        
        return response;
    }

    private validateRequiredFields(body: OperatorRollbackRequest): void {
        const requiredFields = ['customer', 'token', 'gameId', 'trxId'];
        
        for (const field of requiredFields) {
            if (!body[field as keyof OperatorRollbackRequest]) {
                throw new Error(`Required field missing: ${field}`);
            }
        }
    }
}
