import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { PCESService } from "../services/pces.service";
import { OperatorPromoRequest, OperatorPromoResponse, OperatorPromoType } from "@entities/operator.entities";
import * as http from "http";

@Injectable()
export class PromoResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(
        settingsService: mock.SettingsService,
        private readonly pcesService: PCESService
    ) {
        super(settingsService);
    }

    public async getResponse(req: http.IncomingMessage, body: OperatorPromoRequest): Promise<OperatorPromoResponse> {
        console.log(`[PROMO RESPONDER] Processing promo request for customer: ${body.customer}, amount: ${body.amount}, promoType: ${body.promo?.promoType}`);

        // Extract headers
        const genericId = req.headers['generic-id'] as string;
        const hash = req.headers['hash'] as string;

        // Validate required fields
        this.validateRequiredFields(body);

        // Process promo
        const response = await this.pcesService.promo(body, genericId, hash);
        
        console.log(`[PROMO RESPONDER] Promo successful for customer: ${body.customer}, New balance: ${response.balance}, TrxId: ${response.trxId}`);
        
        return response;
    }

    private validateRequiredFields(body: OperatorPromoRequest): void {
        const requiredFields = ['customer', 'token', 'gameId', 'amount', 'currency', 'betId', 'trxId'];
        
        for (const field of requiredFields) {
            if (!body[field as keyof OperatorPromoRequest]) {
                throw new Error(`Required field missing: ${field}`);
            }
        }

        // Validate promo object
        if (!body.promo) {
            throw new Error("Promo object is required");
        }

        if (!body.promo.promoType) {
            throw new Error("Promo type is required");
        }

        if (!body.promo.promoRef) {
            throw new Error("Promo reference is required");
        }

        // Validate promo type
        if (!Object.values(OperatorPromoType).includes(body.promo.promoType)) {
            throw new Error(`Invalid promo type: ${body.promo.promoType}`);
        }

        // Validate amount is positive
        if (body.amount <= 0) {
            throw new Error("Amount must be positive");
        }

        // Validate freespin data if present
        if (body.promo.freeSpinData) {
            this.validateFreespinData(body.promo.freeSpinData);
        }
    }

    private validateFreespinData(freeSpinData: any): void {
        if (!freeSpinData.freespinRef) {
            throw new Error("Freespin reference is required");
        }

        if (freeSpinData.remainingRounds !== undefined && freeSpinData.remainingRounds < 0) {
            throw new Error("Remaining rounds cannot be negative");
        }

        if (freeSpinData.totalWinnings !== undefined && freeSpinData.totalWinnings < 0) {
            throw new Error("Total winnings cannot be negative");
        }
    }
}
