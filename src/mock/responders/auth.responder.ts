import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { PCESService } from "../services/pces.service";
import { OperatorAuthRequest, OperatorAuthResponse } from "@entities/operator.entities";
import * as http from "http";

@Injectable()
export class AuthResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(
        settingsService: mock.SettingsService,
        private readonly pcesService: PCESService
    ) {
        super(settingsService);
    }

    public async getResponse(req: http.IncomingMessage, body: OperatorAuthRequest): Promise<OperatorAuthResponse> {
        console.log(`[AUTH RESPONDER] Processing auth request for customer: ${body.customer}`);

        // Extract headers
        const genericId = req.headers['generic-id'] as string;
        const hash = req.headers['hash'] as string;

        // Validate required fields
        if (!body.customer) {
            throw new Error("Customer field is required");
        }

        if (!body.token) {
            throw new Error("Token field is required");
        }

        // Process authentication
        const response = await this.pcesService.authenticate(body, genericId, hash);
        
        console.log(`[AUTH RESPONDER] Auth successful for customer: ${body.customer}, Balance: ${response.balance}`);
        
        return response;
    }
}
