import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { PCESService } from "../services/pces.service";
import { OperatorCreditRequest, OperatorCreditResponse } from "@entities/operator.entities";
import * as http from "http";

@Injectable()
export class CreditResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(
        settingsService: mock.SettingsService,
        private readonly pcesService: PCESService
    ) {
        super(settingsService);
    }

    public async getResponse(req: http.IncomingMessage, body: OperatorCreditRequest): Promise<OperatorCreditResponse> {
        console.log(`[CREDIT RESPONDER] Processing credit request for customer: ${body.customer}, amount: ${body.amount}`);

        // Extract headers
        const genericId = req.headers['generic-id'] as string;
        const hash = req.headers['hash'] as string;

        // Validate required fields
        this.validateRequiredFields(body);

        // Process credit
        const response = await this.pcesService.credit(body, genericId, hash);
        
        console.log(`[CREDIT RESPONDER] Credit successful for customer: ${body.customer}, New balance: ${response.balance}, TrxId: ${response.trxId}`);
        
        return response;
    }

    private validateRequiredFields(body: OperatorCreditRequest): void {
        const requiredFields = ['customer', 'token', 'gameId', 'amount', 'currency', 'betId', 'trxId'];
        
        for (const field of requiredFields) {
            if (!body[field as keyof OperatorCreditRequest]) {
                throw new Error(`Required field missing: ${field}`);
            }
        }

        // Validate amount is positive
        if (body.amount <= 0) {
            throw new Error("Amount must be positive");
        }
    }
}
