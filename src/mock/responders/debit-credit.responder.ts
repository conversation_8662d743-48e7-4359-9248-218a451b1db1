import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { PCESService } from "../services/pces.service";
import { OperatorDebitCreditRequest, OperatorDebitCreditResponse } from "@entities/operator.entities";
import * as http from "http";

@Injectable()
export class DebitCreditResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(
        settingsService: mock.SettingsService,
        private readonly pcesService: PCESService
    ) {
        super(settingsService);
    }

    public async getResponse(req: http.IncomingMessage, body: OperatorDebitCreditRequest): Promise<OperatorDebitCreditResponse> {
        console.log(`[DEBIT-CREDIT RESPONDER] Processing debit-credit request for customer: ${body.customer}, debit: ${body.amount}, credit: ${body.creditAmount}`);

        // Extract headers
        const genericId = req.headers['generic-id'] as string;
        const hash = req.headers['hash'] as string;

        // Validate required fields
        this.validateRequiredFields(body);

        // Process debit-credit
        const response = await this.pcesService.debitCredit(body, genericId, hash);
        
        console.log(`[DEBIT-CREDIT RESPONDER] Debit-Credit successful for customer: ${body.customer}, New balance: ${response.balance}, DebitTrxId: ${response.trxId}, CreditTrxId: ${response.creditTrxId}`);
        
        return response;
    }

    private validateRequiredFields(body: OperatorDebitCreditRequest): void {
        const requiredFields = ['customer', 'token', 'gameId', 'amount', 'creditAmount', 'currency', 'betId', 'trxId', 'creditTrxId'];
        
        for (const field of requiredFields) {
            if (!body[field as keyof OperatorDebitCreditRequest]) {
                throw new Error(`Required field missing: ${field}`);
            }
        }

        // Validate amounts are positive
        if (body.amount <= 0) {
            throw new Error("Debit amount must be positive");
        }

        if (body.creditAmount < 0) {
            throw new Error("Credit amount cannot be negative");
        }
    }
}
