import { Injectable, NestMiddleware } from "@nestjs/common";
import { Request, Response, NextFunction } from "express";
import { HashValidatorService } from "../utils/hash-validator";

@Injectable()
export class HashValidationMiddleware implements NestMiddleware {
    constructor(private readonly hashValidator: HashValidatorService) {}

    use(req: Request, res: Response, next: NextFunction): void {
        try {
            // Extract headers
            const genericId = req.headers['generic-id'] as string;
            const hash = req.headers['hash'] as string;

            // Log request for debugging
            console.log(`[HASH MIDDLEWARE] ${req.method} ${req.path}`);
            console.log(`[HASH MIDDLEWARE] Generic-Id: ${genericId}`);
            console.log(`[HASH MIDDLEWARE] Hash: ${hash}`);
            console.log(`[HASH MIDDLEWARE] Body:`, req.body);

            // Validate hash
            this.hashValidator.validateHash(req.body, genericId, hash);

            next();
        } catch (error) {
            console.error(`[HASH MIDDLEWARE] Validation failed:`, error.message);
            
            // Return PCES-formatted error response
            const errorResponse = {
                balance: 0,
                bonusBalance: 0,
                code: (error as any).code || 4,
                currency: "EUR",
                status: (error as any).status || "UNAUTHORIZED_REQUEST"
            };

            res.status(401).json(errorResponse);
        }
    }
}
