import { Body, Controller, Post, UseFilters, UseInterceptors, Headers, HttpCode } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { PCESService } from "../services/pces.service";
import { PCESErrorFilter } from "../filters/pces-error.filter";
import {
    OperatorAuthRequest,
    OperatorAuthResponse,
    OperatorDebitRequest,
    OperatorDebitResponse,
    OperatorCreditRequest,
    OperatorCreditResponse,
    OperatorDebitCreditRequest,
    OperatorDebitCreditResponse,
    OperatorRollbackRequest,
    OperatorRollbackResponse,
    OperatorPromoRequest,
    OperatorPromoResponse
} from "@entities/operator.entities";

@Controller()
@UseInterceptors(mock.CustomErrorInterceptor)
@UseFilters(PCESErrorFilter)
export class PCESController {
    constructor(private readonly pcesService: PCESService) {}

    /**
     * Authentication endpoint - Get player balance
     * POST /auth
     */
    @Post("auth")
    @HttpCode(200)
    public async auth(
        @Body() body: OperatorAuthRequest,
        @Headers("Generic-Id") genericId: string,
        @Headers("Hash") hash: string
    ): Promise<OperatorAuthResponse> {
        console.log(`[PCES MOCK] POST /auth - Customer: ${body.customer}`);
        return this.pcesService.authenticate(body, genericId, hash);
    }

    /**
     * Debit endpoint - Place bet (debit player balance)
     * POST /debit
     */
    @Post("debit")
    @HttpCode(200)
    public async debit(
        @Body() body: OperatorDebitRequest,
        @Headers("Generic-Id") genericId: string,
        @Headers("Hash") hash: string
    ): Promise<OperatorDebitResponse> {
        console.log(`[PCES MOCK] POST /debit - Customer: ${body.customer}, Amount: ${body.amount}, BetId: ${body.betId}`);
        return this.pcesService.debit(body, genericId, hash);
    }

    /**
     * Credit endpoint - Settle win (credit player balance)
     * POST /credit
     */
    @Post("credit")
    @HttpCode(200)
    public async credit(
        @Body() body: OperatorCreditRequest,
        @Headers("Generic-Id") genericId: string,
        @Headers("Hash") hash: string
    ): Promise<OperatorCreditResponse> {
        console.log(`[PCES MOCK] POST /credit - Customer: ${body.customer}, Amount: ${body.amount}, BetId: ${body.betId}`);
        return this.pcesService.credit(body, genericId, hash);
    }

    /**
     * Debit-Credit endpoint - Combined bet and settlement
     * POST /debit-credit
     */
    @Post("debit-credit")
    @HttpCode(200)
    public async debitCredit(
        @Body() body: OperatorDebitCreditRequest,
        @Headers("Generic-Id") genericId: string,
        @Headers("Hash") hash: string
    ): Promise<OperatorDebitCreditResponse> {
        console.log(`[PCES MOCK] POST /debit-credit - Customer: ${body.customer}, Debit: ${body.amount}, Credit: ${body.creditAmount}`);
        return this.pcesService.debitCredit(body, genericId, hash);
    }

    /**
     * Rollback endpoint - Reverse transaction
     * POST /rollback
     */
    @Post("rollback")
    @HttpCode(200)
    public async rollback(
        @Body() body: OperatorRollbackRequest,
        @Headers("Generic-Id") genericId: string,
        @Headers("Hash") hash: string
    ): Promise<OperatorRollbackResponse> {
        console.log(`[PCES MOCK] POST /rollback - Customer: ${body.customer}, TrxId: ${body.trxId}`);
        return this.pcesService.rollback(body, genericId, hash);
    }

    /**
     * Promo endpoint - Promotional wins (freespins, jackpots, etc.)
     * POST /promo
     */
    @Post("promo")
    @HttpCode(200)
    public async promo(
        @Body() body: OperatorPromoRequest,
        @Headers("Generic-Id") genericId: string,
        @Headers("Hash") hash: string
    ): Promise<OperatorPromoResponse> {
        console.log(`[PCES MOCK] POST /promo - Customer: ${body.customer}, Amount: ${body.amount}, PromoType: ${body.promo?.promoType}`);
        return this.pcesService.promo(body, genericId, hash);
    }
}
