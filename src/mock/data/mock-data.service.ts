import { Injectable } from "@nestjs/common";
import { OperatorPromoType } from "@entities/operator.entities";

export interface MockPlayer {
    customer: string;
    balance: number;
    bonusBalance: number;
    currency: string;
    traderId: number;
    isActive: boolean;
    tokens: string[];
}

export interface MockTransaction {
    id: number;
    externalTrxId: string;
    customer: string;
    gameId: string;
    amount: number;
    type: 'debit' | 'credit' | 'rollback' | 'promo';
    betId?: string;
    status: 'completed' | 'rolledback';
    timestamp: Date;
    promoType?: OperatorPromoType;
}

export interface MockBet {
    betId: string;
    customer: string;
    gameId: string;
    amount: number;
    status: 'active' | 'won' | 'lost';
    timestamp: Date;
}

@Injectable()
export class MockDataService {
    private players: Map<string, MockPlayer> = new Map();
    private transactions: Map<number, MockTransaction> = new Map();
    private bets: Map<string, MockBet> = new Map();
    private transactionCounter = 1000000;

    constructor() {
        this.initializeMockData();
    }

    private initializeMockData(): void {
        // Initialize some test players
        const testPlayers: MockPlayer[] = [
            {
                customer: "2019105152683",
                balance: 120.548,
                bonusBalance: 5.1,
                currency: "TRY",
                traderId: 1,
                isActive: true,
                tokens: ["692A5DF3996581ADD825BEB52A71F54F519AEEBCB2BA0DBCC3D440C2B98887A076D037C7F879D344"]
            },
            {
                customer: "2019045569026",
                balance: 250.75,
                bonusBalance: 10.25,
                currency: "EUR",
                traderId: 2,
                isActive: true,
                tokens: ["TEST_TOKEN_EUR_PLAYER"]
            },
            {
                customer: "2020123456789",
                balance: 1000.00,
                bonusBalance: 50.00,
                currency: "USD",
                traderId: 3,
                isActive: true,
                tokens: ["TEST_TOKEN_USD_PLAYER"]
            },
            {
                customer: "SUSPENDED_PLAYER",
                balance: 0.00,
                bonusBalance: 0.00,
                currency: "EUR",
                traderId: 4,
                isActive: false,
                tokens: ["SUSPENDED_TOKEN"]
            }
        ];

        testPlayers.forEach(player => {
            this.players.set(player.customer, player);
        });

        console.log(`[MOCK DATA] Initialized ${testPlayers.length} test players`);
    }

    getPlayer(customer: string): MockPlayer | undefined {
        return this.players.get(customer);
    }

    isValidToken(token: string, customer: string): boolean {
        const player = this.players.get(customer);
        if (!player || !player.isActive) {
            return false;
        }
        return player.tokens.includes(token);
    }

    updatePlayerBalance(customer: string, amount: number): void {
        const player = this.players.get(customer);
        if (!player) {
            throw new Error("Player not found");
        }

        // Distribute amount between main balance and bonus balance
        if (amount > 0) {
            // For credits, add to main balance
            player.balance += amount;
        } else {
            // For debits, deduct from bonus balance first, then main balance
            const debitAmount = Math.abs(amount);
            if (player.bonusBalance >= debitAmount) {
                player.bonusBalance -= debitAmount;
            } else {
                const remainingDebit = debitAmount - player.bonusBalance;
                player.bonusBalance = 0;
                player.balance -= remainingDebit;
            }
        }

        // Ensure balances don't go negative
        player.balance = Math.max(0, player.balance);
        player.bonusBalance = Math.max(0, player.bonusBalance);
    }

    betExists(betId: string): boolean {
        return this.bets.has(betId);
    }

    betAlreadyWon(betId: string): boolean {
        const bet = this.bets.get(betId);
        return bet ? bet.status === 'won' : false;
    }

    markBetAsWon(betId: string): void {
        const bet = this.bets.get(betId);
        if (bet) {
            bet.status = 'won';
        }
    }

    createDebitTransaction(request: any): MockTransaction {
        const transaction: MockTransaction = {
            id: this.transactionCounter++,
            externalTrxId: request.trxId,
            customer: request.customer,
            gameId: request.gameId,
            amount: request.amount,
            type: 'debit',
            betId: request.betId,
            status: 'completed',
            timestamp: new Date()
        };

        this.transactions.set(transaction.id, transaction);

        // Create bet record
        if (request.betId) {
            const bet: MockBet = {
                betId: request.betId,
                customer: request.customer,
                gameId: request.gameId,
                amount: request.amount,
                status: 'active',
                timestamp: new Date()
            };
            this.bets.set(request.betId, bet);
        }

        return transaction;
    }

    createCreditTransaction(request: any): MockTransaction {
        const transaction: MockTransaction = {
            id: this.transactionCounter++,
            externalTrxId: request.trxId,
            customer: request.customer,
            gameId: request.gameId,
            amount: request.amount,
            type: 'credit',
            betId: request.betId,
            status: 'completed',
            timestamp: new Date()
        };

        this.transactions.set(transaction.id, transaction);
        return transaction;
    }

    createRollbackTransaction(request: any, originalTransaction: MockTransaction): MockTransaction {
        const transaction: MockTransaction = {
            id: this.transactionCounter++,
            externalTrxId: `rollback_${originalTransaction.externalTrxId}`,
            customer: request.customer,
            gameId: originalTransaction.gameId,
            amount: originalTransaction.amount,
            type: 'rollback',
            betId: originalTransaction.betId,
            status: 'completed',
            timestamp: new Date()
        };

        this.transactions.set(transaction.id, transaction);
        return transaction;
    }

    createPromoTransaction(request: any): MockTransaction {
        const transaction: MockTransaction = {
            id: this.transactionCounter++,
            externalTrxId: request.trxId,
            customer: request.customer,
            gameId: request.gameId,
            amount: request.amount,
            type: 'promo',
            status: 'completed',
            timestamp: new Date(),
            promoType: request.promo.promoType
        };

        this.transactions.set(transaction.id, transaction);
        return transaction;
    }

    getTransactionByTrxId(trxId: string): MockTransaction | undefined {
        for (const transaction of this.transactions.values()) {
            if (transaction.externalTrxId === trxId) {
                return transaction;
            }
        }
        return undefined;
    }

    markTransactionAsRolledBack(transactionId: number): void {
        const transaction = this.transactions.get(transactionId);
        if (transaction) {
            transaction.status = 'rolledback';
        }
    }

    isValidPromoType(promoType: OperatorPromoType): boolean {
        return Object.values(OperatorPromoType).includes(promoType);
    }

    // Utility methods for testing scenarios
    addTestPlayer(player: MockPlayer): void {
        this.players.set(player.customer, player);
    }

    removePlayer(customer: string): void {
        this.players.delete(customer);
    }

    getAllPlayers(): MockPlayer[] {
        return Array.from(this.players.values());
    }

    getAllTransactions(): MockTransaction[] {
        return Array.from(this.transactions.values());
    }

    getAllBets(): MockBet[] {
        return Array.from(this.bets.values());
    }

    resetData(): void {
        this.players.clear();
        this.transactions.clear();
        this.bets.clear();
        this.transactionCounter = 1000000;
        this.initializeMockData();
    }
}
