#!/usr/bin/env node

/**
 * Simple test script to verify PCES Mock Server functionality
 * This script tests all endpoints with sample data
 */

const http = require('http');
const crypto = require('crypto');

// Configuration
const MOCK_SERVER_URL = 'http://localhost:3001';
const SECRET_KEY = 'mock_secret_key_for_testing';
const GENERIC_ID = 'test_generic_id';

// Test data
const TEST_CUSTOMER = '2019105152683';
const TEST_TOKEN = '692A5DF3996581ADD825BEB52A71F54F519AEEBCB2BA0DBCC3D440C2B98887A076D037C7F879D344';

/**
 * Calculate HMAC-SHA256 hash for PCES API
 */
function calculateHash(requestBody, genericId) {
    const dataToHash = { ...requestBody, genericId };
    const sortedKeys = Object.keys(dataToHash).sort();
    const values = [];
    
    for (const key of sortedKeys) {
        const value = dataToHash[key];
        if (value !== null && value !== undefined) {
            if (typeof value === 'object') {
                values.push(JSON.stringify(value));
            } else {
                values.push(String(value));
            }
        }
    }
    
    const stringToHash = values.join('');
    const hmac = crypto.createHmac('sha256', SECRET_KEY);
    hmac.update(stringToHash);
    return hmac.digest('hex').toUpperCase();
}

/**
 * Make HTTP request to mock server
 */
function makeRequest(endpoint, data) {
    return new Promise((resolve, reject) => {
        const hash = calculateHash(data, GENERIC_ID);
        const postData = JSON.stringify(data);
        
        const options = {
            hostname: 'localhost',
            port: 3001,
            path: endpoint,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData),
                'Generic-Id': GENERIC_ID,
                'Hash': hash
            }
        };
        
        const req = http.request(options, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = JSON.parse(responseData);
                    resolve({ statusCode: res.statusCode, data: response });
                } catch (error) {
                    resolve({ statusCode: res.statusCode, data: responseData });
                }
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        req.write(postData);
        req.end();
    });
}

/**
 * Test all PCES endpoints
 */
async function testPCESEndpoints() {
    console.log('🧪 Testing PCES Mock Server Endpoints\n');
    
    const tests = [
        {
            name: 'Authentication',
            endpoint: '/auth',
            data: {
                customer: TEST_CUSTOMER,
                token: TEST_TOKEN
            }
        },
        {
            name: 'Debit (Place Bet)',
            endpoint: '/debit',
            data: {
                customer: TEST_CUSTOMER,
                token: TEST_TOKEN,
                gameId: 'test_game',
                amount: 10.0,
                currency: 'TRY',
                betId: 'bet_' + Date.now(),
                trxId: 'trx_debit_' + Date.now(),
                tip: false
            }
        },
        {
            name: 'Credit (Settle Win)',
            endpoint: '/credit',
            data: {
                customer: TEST_CUSTOMER,
                token: TEST_TOKEN,
                gameId: 'test_game',
                amount: 25.0,
                currency: 'TRY',
                betId: 'bet_' + (Date.now() - 1000),
                trxId: 'trx_credit_' + Date.now()
            }
        },
        {
            name: 'Debit-Credit (Combined)',
            endpoint: '/debit-credit',
            data: {
                customer: TEST_CUSTOMER,
                token: TEST_TOKEN,
                gameId: 'test_game',
                amount: 5.0,
                creditAmount: 15.0,
                currency: 'TRY',
                betId: 'bet_combined_' + Date.now(),
                trxId: 'trx_debit_combined_' + Date.now(),
                creditTrxId: 'trx_credit_combined_' + Date.now(),
                tip: false
            }
        },
        {
            name: 'Promo (Promotional Win)',
            endpoint: '/promo',
            data: {
                customer: TEST_CUSTOMER,
                token: TEST_TOKEN,
                gameId: 'test_game',
                amount: 50.0,
                currency: 'TRY',
                betId: 'bet_promo_' + Date.now(),
                trxId: 'trx_promo_' + Date.now(),
                promo: {
                    promoType: 'FSW',
                    promoRef: 'freespin_promo_123',
                    freeSpinData: {
                        freespinRef: 'fs_ref_123',
                        requested: true,
                        remainingRounds: 10,
                        totalWinnings: 50.0
                    }
                }
            }
        }
    ];
    
    for (const test of tests) {
        try {
            console.log(`📡 Testing ${test.name}...`);
            const result = await makeRequest(test.endpoint, test.data);
            
            if (result.statusCode === 200 && result.data.code === 0) {
                console.log(`✅ ${test.name} - SUCCESS`);
                console.log(`   Balance: ${result.data.balance}, Bonus: ${result.data.bonusBalance}`);
                if (result.data.trxId) {
                    console.log(`   Transaction ID: ${result.data.trxId}`);
                }
            } else {
                console.log(`❌ ${test.name} - FAILED`);
                console.log(`   Status: ${result.statusCode}, Response:`, result.data);
            }
        } catch (error) {
            console.log(`❌ ${test.name} - ERROR: ${error.message}`);
        }
        console.log('');
    }
}

/**
 * Test error scenarios
 */
async function testErrorScenarios() {
    console.log('🚨 Testing Error Scenarios\n');
    
    const errorTests = [
        {
            name: 'Invalid Customer',
            endpoint: '/auth',
            data: {
                customer: 'INVALID_CUSTOMER',
                token: 'INVALID_TOKEN'
            },
            expectedCode: 22
        },
        {
            name: 'Insufficient Funds',
            endpoint: '/debit',
            data: {
                customer: TEST_CUSTOMER,
                token: TEST_TOKEN,
                gameId: 'test_game',
                amount: 10000.0,
                currency: 'TRY',
                betId: 'bet_large_' + Date.now(),
                trxId: 'trx_large_' + Date.now()
            },
            expectedCode: 24
        }
    ];
    
    for (const test of errorTests) {
        try {
            console.log(`📡 Testing ${test.name}...`);
            const result = await makeRequest(test.endpoint, test.data);
            
            if (result.data.code === test.expectedCode) {
                console.log(`✅ ${test.name} - Correct error code ${test.expectedCode}`);
            } else {
                console.log(`❌ ${test.name} - Expected code ${test.expectedCode}, got ${result.data.code}`);
            }
        } catch (error) {
            console.log(`❌ ${test.name} - ERROR: ${error.message}`);
        }
        console.log('');
    }
}

/**
 * Check if mock server is running
 */
function checkServerHealth() {
    return new Promise((resolve) => {
        const req = http.request({
            hostname: 'localhost',
            port: 3001,
            path: '/health',
            method: 'GET'
        }, (res) => {
            resolve(res.statusCode === 200);
        });
        
        req.on('error', () => {
            resolve(false);
        });
        
        req.end();
    });
}

/**
 * Main test function
 */
async function main() {
    console.log('🚀 PCES Mock Server Test Suite\n');
    
    // Check if server is running
    const isServerRunning = await checkServerHealth();
    
    if (!isServerRunning) {
        console.log('❌ Mock server is not running on http://localhost:3001');
        console.log('   Please start the server with: npm run start:mock');
        console.log('   Or with: npx ts-node src/mainMock.ts');
        process.exit(1);
    }
    
    console.log('✅ Mock server is running\n');
    
    // Run tests
    await testPCESEndpoints();
    await testErrorScenarios();
    
    console.log('🎉 Test suite completed!');
    console.log('\n📖 For more information, see docs/PCES_MOCK_SERVER.md');
}

// Run tests
main().catch(console.error);
