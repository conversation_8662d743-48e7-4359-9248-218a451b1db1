## Description

[Pronet integration](https://confluence.skywindgroup.com/display/SWI/%5BPronet%5D+%28i%29+Integration+Technical+Documentation) - link to confluence

## 🎯 PCES Integration Specifics

### API Endpoints Implemented
- `/auth` - Authentication and balance retrieval
- `/debit` - Bet placement (debit player balance)
- `/credit` - Win settlement (credit player balance)
- `/debit-credit` - Combined bet and settlement
- `/rollback` - Transaction reversal
- `/promo` - Promotional wins (freespins, jackpots, etc.)

### Security Features
- **HMAC-SHA256 Hash Validation** for all requests
- **Generic-Id Header Authentication**
- **Token-based Player Authentication**
- **Input Validation** with comprehensive error handling

## 🧪 PCES Mock Server

A comprehensive mock server implementation is available for development and testing purposes.

### Quick Start

```bash
# Start the mock server
npm run start:mock

# Test the mock server
node test-mock-server.js
```

### Features
- ✅ **Complete PCES API Coverage**: All 6 endpoints implemented
- ✅ **Realistic Mock Data**: Pre-configured test players with balances
- ✅ **HMAC-SHA256 Hash Validation**: Proper security validation (can be disabled for development)
- ✅ **Error <PERSON>**: Various error codes and conditions for testing
- ✅ **Request/Response Logging**: Detailed logging for debugging
- ✅ **Configurable Responses**: Different scenarios for comprehensive testing

### Test Players Available
- **Turkish Lira Player**: `2019105152683` (Balance: 120.548 TRY)
- **Euro Player**: `2019045569026` (Balance: 250.75 EUR)
- **USD Player**: `2020123456789` (Balance: 1000.00 USD)
- **Suspended Player**: `SUSPENDED_PLAYER` (For error testing)

### Documentation
See [docs/PCES_MOCK_SERVER.md](docs/PCES_MOCK_SERVER.md) for complete documentation including:
- Detailed endpoint specifications
- Request/response examples
- Error code reference
- Configuration options
- Integration guidelines
